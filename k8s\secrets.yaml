# IMPORTANT: This is a template file. 
# DO NOT commit actual secrets to version control.
# Use this template to create your actual secrets.yaml file with real values.
# 
# To create secrets from this template:
# 1. Copy this file to secrets-actual.yaml
# 2. Replace all placeholder values with actual base64-encoded secrets
# 3. Apply: kubectl apply -f secrets-actual.yaml
# 4. Add secrets-actual.yaml to .gitignore

apiVersion: v1
kind: Secret
metadata:
  name: whatsapp-hotel-bot-secrets
  namespace: hotel-bot
  labels:
    app: whatsapp-hotel-bot
type: Opaque
data:
  # Database connection string (base64 encoded)
  # Example: postgresql+asyncpg://user:password@postgres:5432/hotel_bot
  database-url: cG9zdGdyZXNxbCthc3luY3BnOi8vdXNlcjpwYXNzd29yZEBwb3N0Z3Jlczo1NDMyL2hvdGVsX2JvdA==
  
  # Redis connection string (base64 encoded)
  # Example: redis://:password@redis:6379
  redis-url: cmVkaXM6Ly86cGFzc3dvcmRAcmVkaXM6NjM3OQ==
  
  # Celery broker URL (base64 encoded)
  # Example: redis://:password@redis:6379/0
  celery-broker-url: cmVkaXM6Ly86cGFzc3dvcmRAcmVkaXM6NjM3OS8w
  
  # Celery result backend (base64 encoded)
  # Example: redis://:password@redis:6379/1
  celery-result-backend: cmVkaXM6Ly86cGFzc3dvcmRAcmVkaXM6NjM3OS8x
  
  # Green API credentials (base64 encoded)
  green-api-instance-id: eW91cl9pbnN0YW5jZV9pZF9oZXJl
  green-api-token: eW91cl9hcGlfdG9rZW5faGVyZQ==
  
  # DeepSeek API key (base64 encoded)
  deepseek-api-key: eW91cl9kZWVwc2Vla19hcGlfa2V5X2hlcmU=
  
  # Application secret key (base64 encoded)
  # Generate with: python -c "import secrets; print(secrets.token_urlsafe(32))"
  secret-key: eW91ci1zdXBlci1zZWNyZXQta2V5LWhlcmUtY2hhbmdlLWluLXByb2R1Y3Rpb24=
  
  # Notification email credentials (base64 encoded)
  notification-email-username: ****************************
  notification-email-password: eW91cl9hcHBfcGFzc3dvcmQ=

---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secrets
  namespace: hotel-bot
  labels:
    app: postgres
type: Opaque
data:
  # PostgreSQL credentials (base64 encoded)
  postgres-password: cGFzc3dvcmQ=
  postgres-user: cG9zdGdyZXM=
  postgres-db: aG90ZWxfYm90

---
apiVersion: v1
kind: Secret
metadata:
  name: redis-secrets
  namespace: hotel-bot
  labels:
    app: redis
type: Opaque
data:
  # Redis password (base64 encoded)
  redis-password: cmVkaXNfcGFzc3dvcmQ=

---
apiVersion: v1
kind: Secret
metadata:
  name: monitoring-auth
  namespace: hotel-bot
  labels:
    app: monitoring
type: Opaque
data:
  # Basic auth for monitoring endpoints (base64 encoded)
  # Format: username:password
  # Example: admin:monitoring_password
  auth: YWRtaW46bW9uaXRvcmluZ19wYXNzd29yZA==

---
# TLS certificates (managed by cert-manager)
apiVersion: v1
kind: Secret
metadata:
  name: whatsapp-hotel-bot-tls
  namespace: hotel-bot
  labels:
    app: whatsapp-hotel-bot
type: kubernetes.io/tls
data:
  # These will be automatically populated by cert-manager
  tls.crt: ""
  tls.key: ""

---
apiVersion: v1
kind: Secret
metadata:
  name: whatsapp-hotel-bot-monitoring-tls
  namespace: hotel-bot
  labels:
    app: whatsapp-hotel-bot
    component: monitoring
type: kubernetes.io/tls
data:
  # These will be automatically populated by cert-manager
  tls.crt: ""
  tls.key: ""

---
# Docker registry secret (if using private registry)
apiVersion: v1
kind: Secret
metadata:
  name: docker-registry-secret
  namespace: hotel-bot
type: kubernetes.io/dockerconfigjson
data:
  # Docker registry credentials (base64 encoded)
  # Generate with: kubectl create secret docker-registry docker-registry-secret \
  #   --docker-server=your-registry-server \
  #   --docker-username=your-username \
  #   --docker-password=your-password \
  #   --docker-email=your-email \
  #   --dry-run=client -o yaml
  .dockerconfigjson: ********************************************************************************************************************************************************************************************************************

---
# Instructions for creating actual secrets:
# 
# 1. Create namespace:
#    kubectl create namespace hotel-bot
# 
# 2. Encode your secrets to base64:
#    echo -n "your-secret-value" | base64
# 
# 3. Replace placeholder values in this file
# 
# 4. Apply secrets:
#    kubectl apply -f secrets-actual.yaml
# 
# 5. Verify secrets:
#    kubectl get secrets -n hotel-bot
#    kubectl describe secret whatsapp-hotel-bot-secrets -n hotel-bot
# 
# 6. To decode and verify a secret:
#    kubectl get secret whatsapp-hotel-bot-secrets -n hotel-bot -o jsonpath="{.data.secret-key}" | base64 --decode
