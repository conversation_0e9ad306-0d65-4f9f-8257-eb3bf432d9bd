# Multi-stage production build for WhatsApp Hotel Bot
# Optimized for production deployment with security hardening

# Build stage
FROM python:3.11-slim as builder

# Set build arguments
ARG BUILD_DATE
ARG VCS_REF
ARG VERSION

# Set environment variables for build
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_DEFAULT_TIMEOUT=100

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create and activate virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements first for better caching
COPY requirements.txt requirements-prod.txt* ./

# Install Python dependencies
RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements.txt && \
    if [ -f requirements-prod.txt ]; then pip install -r requirements-prod.txt; fi

# Production stage
FROM python:3.11-slim as production

# Set build metadata
LABEL maintainer="Hotel Boost Team" \
      version="${VERSION}" \
      description="WhatsApp Hotel Bot - Production Image" \
      build-date="${BUILD_DATE}" \
      vcs-ref="${VCS_REF}"

# Set production environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PATH="/opt/venv/bin:$PATH" \
    ENVIRONMENT=production \
    DEBUG=false

# Install runtime dependencies and security updates
RUN apt-get update && apt-get install -y \
    curl \
    dumb-init \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && apt-get autoremove -y

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create non-root user with specific UID/GID for security
RUN groupadd -r -g 1001 appuser && \
    useradd -r -g appuser -u 1001 -d /app -s /bin/bash appuser

# Set work directory
WORKDIR /app

# Copy application code with proper ownership
COPY --chown=appuser:appuser . .

# Remove development files and sensitive data
RUN rm -rf \
    .git \
    .github \
    .vscode \
    .pytest_cache \
    __pycache__ \
    *.pyc \
    *.pyo \
    *.pyd \
    .Python \
    env \
    pip-log.txt \
    pip-delete-this-directory.txt \
    .tox \
    .coverage \
    .coverage.* \
    .cache \
    nosetests.xml \
    coverage.xml \
    *.cover \
    *.log \
    .DS_Store \
    .env.example \
    docker-compose.yml \
    Dockerfile \
    README.md \
    docs/ \
    tests/

# Create necessary directories
RUN mkdir -p /app/logs /app/tmp && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Health check with improved reliability
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Run the application with production settings
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4", "--access-log"]
