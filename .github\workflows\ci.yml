name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: "3.11"
  POETRY_VERSION: "1.6.1"

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
          POSTGRES_DB: hotel_bot_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Lint with flake8
      run: |
        flake8 app --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 app --count --exit-zero --max-complexity=10 --max-line-length=88 --statistics

    - name: Format check with black
      run: |
        black --check app

    - name: Import sort check with isort
      run: |
        isort --check-only app

    - name: Type check with mypy
      run: |
        mypy app

    - name: Run unit tests
      env:
        DATABASE_URL: postgresql+asyncpg://postgres:password@localhost:5432/hotel_bot_test
        REDIS_URL: redis://localhost:6379
        SECRET_KEY: test-secret-key
        ENVIRONMENT: test
      run: |
        pytest tests/unit/ -v -m "unit" --cov=app --cov-report=xml --cov-report=html --cov-append

    - name: Run integration tests
      env:
        DATABASE_URL: postgresql+asyncpg://postgres:password@localhost:5432/hotel_bot_test
        REDIS_URL: redis://localhost:6379
        SECRET_KEY: test-secret-key
        ENVIRONMENT: test
      run: |
        pytest tests/integration/ -v -m "integration" --cov=app --cov-append

    - name: Run security tests
      env:
        DATABASE_URL: postgresql+asyncpg://postgres:password@localhost:5432/hotel_bot_test
        REDIS_URL: redis://localhost:6379
        SECRET_KEY: test-secret-key
        ENVIRONMENT: test
      run: |
        pytest tests/security/ -v -m "security" --cov=app --cov-append

    - name: Run performance tests
      env:
        DATABASE_URL: postgresql+asyncpg://postgres:password@localhost:5432/hotel_bot_test
        REDIS_URL: redis://localhost:6379
        SECRET_KEY: test-secret-key
        ENVIRONMENT: test
      run: |
        pytest tests/performance/ -v -m "performance and not stress" --cov=app --cov-append

    - name: Run smoke tests
      env:
        DATABASE_URL: postgresql+asyncpg://postgres:password@localhost:5432/hotel_bot_test
        REDIS_URL: redis://localhost:6379
        SECRET_KEY: test-secret-key
        ENVIRONMENT: test
      run: |
        pytest tests/ -v -m "smoke" --cov=app --cov-append

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety

    - name: Security check with bandit
      run: |
        bandit -r app -f json -o bandit-report.json

    - name: Safety check
      run: |
        safety check --json --output safety-report.json

  docker:
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/whatsapp-hotel-bot:latest
          ${{ secrets.DOCKER_USERNAME }}/whatsapp-hotel-bot:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy:
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to production
      run: |
        echo "Deployment step - configure based on your infrastructure"
        # Add your deployment commands here
