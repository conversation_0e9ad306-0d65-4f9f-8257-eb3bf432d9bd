version: '3.8'

services:
  # FastAPI application - Production
  api:
    build: 
      context: .
      dockerfile: Dockerfile.prod
      args:
        BUILD_DATE: ${BUILD_DATE:-}
        VCS_REF: ${VCS_REF:-}
        VERSION: ${VERSION:-1.0.0}
    image: whatsapp-hotel-bot:${VERSION:-latest}
    container_name: whatsapp-hotel-bot-api-prod
    restart: unless-stopped
    ports:
      - "${API_PORT:-8000}:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - GREEN_API_INSTANCE_ID=${GREEN_API_INSTANCE_ID}
      - GREEN_API_TOKEN=${GREEN_API_TOKEN}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - PROMETHEUS_ENABLED=${PROMETHEUS_ENABLED:-true}
      - GRAFANA_ENABLED=${GRAFANA_ENABLED:-true}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - app_logs:/app/logs
      - app_tmp:/app/tmp
    networks:
      - hotel-bot-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL database - Production
  db:
    image: postgres:15-alpine
    container_name: whatsapp-hotel-bot-db-prod
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - postgres_backups:/backups
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - hotel-bot-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis cache - Production
  redis:
    image: redis:7-alpine
    container_name: whatsapp-hotel-bot-redis-prod
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data_prod:/data
    networks:
      - hotel-bot-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Celery worker - Production
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.prod
      args:
        BUILD_DATE: ${BUILD_DATE:-}
        VCS_REF: ${VCS_REF:-}
        VERSION: ${VERSION:-1.0.0}
    image: whatsapp-hotel-bot:${VERSION:-latest}
    container_name: whatsapp-hotel-bot-celery-prod
    restart: unless-stopped
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - GREEN_API_INSTANCE_ID=${GREEN_API_INSTANCE_ID}
      - GREEN_API_TOKEN=${GREEN_API_TOKEN}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - app_logs:/app/logs
      - app_tmp:/app/tmp
    command: celery -A app.core.celery worker --loglevel=info --concurrency=4
    networks:
      - hotel-bot-network
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 1.5G
        reservations:
          cpus: '0.25'
          memory: 256M

  # Celery beat scheduler - Production
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.prod
      args:
        BUILD_DATE: ${BUILD_DATE:-}
        VCS_REF: ${VCS_REF:-}
        VERSION: ${VERSION:-1.0.0}
    image: whatsapp-hotel-bot:${VERSION:-latest}
    container_name: whatsapp-hotel-bot-beat-prod
    restart: unless-stopped
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - app_logs:/app/logs
      - celery_beat_data:/app/celerybeat-schedule
    command: celery -A app.core.celery beat --loglevel=info
    networks:
      - hotel-bot-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

  # Prometheus monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: whatsapp-hotel-bot-prometheus
    restart: unless-stopped
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/alerts.yml:/etc/prometheus/alerts.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - hotel-bot-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  # Grafana dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: whatsapp-hotel-bot-grafana
    restart: unless-stopped
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana-dashboard.json:/etc/grafana/provisioning/dashboards/dashboard.json:ro
    networks:
      - hotel-bot-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

volumes:
  postgres_data_prod:
    driver: local
  postgres_backups:
    driver: local
  redis_data_prod:
    driver: local
  app_logs:
    driver: local
  app_tmp:
    driver: local
  celery_beat_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  hotel-bot-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
