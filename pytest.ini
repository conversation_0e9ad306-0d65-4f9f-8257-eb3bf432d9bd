[tool:pytest]
# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --asyncio-mode=auto
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=85
    --cov-branch

# Markers
markers =
    unit: Unit tests for individual components
    integration: Integration tests for database operations
    performance: Performance and load tests
    security: Security and authentication tests
    isolation: Data isolation and tenant security tests
    benchmark: Performance benchmark tests
    stress: Stress testing under extreme load
    slow: Slow running tests (excluded by default)
    smoke: Quick smoke tests for basic functionality
    green_api: Green API related tests
    deepseek: DeepSeek AI related tests
    webhook: Webhook related tests
    celery: Celery task tests
    message_processing: Message processing tests
    sentiment: Sentiment analysis tests
    trigger: Trigger engine tests
    notification: Notification system tests
    api: API endpoint tests
    database: Database operation tests
    mock: Tests using mock services
    fixture: Tests using fixtures
    e2e: End-to-end workflow tests

# Test timeout (in seconds)
timeout = 300

# Async test configuration
asyncio_mode = auto

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*
    ignore::UserWarning:celery.*
    ignore::UserWarning:httpx.*
