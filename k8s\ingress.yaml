apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: whatsapp-hotel-bot-ingress
  namespace: hotel-bot
  labels:
    app: whatsapp-hotel-bot
  annotations:
    # NGINX Ingress Controller annotations
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # CORS settings
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    
    # Security headers
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header X-Frame-Options "SAMEORIGIN" always;
      add_header X-Content-Type-Options "nosniff" always;
      add_header X-XSS-Protection "1; mode=block" always;
      add_header Referrer-Policy "strict-origin-when-cross-origin" always;
      add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'self';" always;
    
    # Load balancing
    nginx.ingress.kubernetes.io/upstream-hash-by: "$remote_addr"
    
    # Timeouts
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    
    # Body size
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    
    # SSL/TLS
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
spec:
  tls:
  - hosts:
    - api.hotel-bot.example.com
    - hotel-bot.example.com
    secretName: whatsapp-hotel-bot-tls
  rules:
  - host: api.hotel-bot.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: whatsapp-hotel-bot-api
            port:
              number: 8000
  - host: hotel-bot.example.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: whatsapp-hotel-bot-api
            port:
              number: 8000
      - path: /docs
        pathType: Prefix
        backend:
          service:
            name: whatsapp-hotel-bot-api
            port:
              number: 8000
      - path: /redoc
        pathType: Prefix
        backend:
          service:
            name: whatsapp-hotel-bot-api
            port:
              number: 8000
      - path: /health
        pathType: Prefix
        backend:
          service:
            name: whatsapp-hotel-bot-api
            port:
              number: 8000

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: whatsapp-hotel-bot-monitoring-ingress
  namespace: hotel-bot
  labels:
    app: whatsapp-hotel-bot
    component: monitoring
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    
    # Authentication for monitoring endpoints
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: monitoring-auth
    nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Monitoring"
    
    # Rate limiting for monitoring
    nginx.ingress.kubernetes.io/rate-limit: "10"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    
spec:
  tls:
  - hosts:
    - monitoring.hotel-bot.example.com
    secretName: whatsapp-hotel-bot-monitoring-tls
  rules:
  - host: monitoring.hotel-bot.example.com
    http:
      paths:
      - path: /prometheus
        pathType: Prefix
        backend:
          service:
            name: prometheus
            port:
              number: 9090
      - path: /grafana
        pathType: Prefix
        backend:
          service:
            name: grafana
            port:
              number: 3000

---
# Network Policy for ingress traffic
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: whatsapp-hotel-bot-ingress-policy
  namespace: hotel-bot
spec:
  podSelector:
    matchLabels:
      app: whatsapp-hotel-bot
      component: api
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  - from:
    - namespaceSelector:
        matchLabels:
          name: hotel-bot
    ports:
    - protocol: TCP
      port: 8000
