apiVersion: v1
kind: Service
metadata:
  name: whatsapp-hotel-bot-api
  namespace: hotel-bot
  labels:
    app: whatsapp-hotel-bot
    component: api
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: whatsapp-hotel-bot
    component: api

---
apiVersion: v1
kind: Service
metadata:
  name: whatsapp-hotel-bot-api-headless
  namespace: hotel-bot
  labels:
    app: whatsapp-hotel-bot
    component: api
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: whatsapp-hotel-bot
    component: api

---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: hotel-bot
  labels:
    app: postgres
    component: database
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: postgres
    component: database

---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: hotel-bot
  labels:
    app: redis
    component: cache
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app: redis
    component: cache

---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: hotel-bot
  labels:
    app: prometheus
    component: monitoring
spec:
  type: ClusterIP
  ports:
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: prometheus
  selector:
    app: prometheus
    component: monitoring

---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: hotel-bot
  labels:
    app: grafana
    component: monitoring
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: grafana
  selector:
    app: grafana
    component: monitoring

---
# External service for load balancer (if needed)
apiVersion: v1
kind: Service
metadata:
  name: whatsapp-hotel-bot-api-external
  namespace: hotel-bot
  labels:
    app: whatsapp-hotel-bot
    component: api
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "tcp"
    service.beta.kubernetes.io/aws-load-balancer-cross-zone-load-balancing-enabled: "true"
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  - port: 443
    targetPort: 8000
    protocol: TCP
    name: https
  selector:
    app: whatsapp-hotel-bot
    component: api
  sessionAffinity: None
